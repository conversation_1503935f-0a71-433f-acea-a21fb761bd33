<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆包图像生成</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .help-text {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .examples {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .examples h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .example-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .example-item:hover {
            background-color: #e9ecef;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 豆包图像生成</h1>
            <p>基于豆包AI的智能图像生成服务</p>
        </div>
        
        <div class="form-container">
            <form id="generateForm" action="/generate-form" method="post">
                <div class="form-group">
                    <label for="prompt">提示词 *</label>
                    <textarea id="prompt" name="prompt" placeholder="描述您想要生成的图像..." required></textarea>
                    <div class="help-text">详细描述您想要的图像内容、风格、色彩等</div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="size">图像尺寸</label>
                        <select id="size" name="size">
                            <option value="512x512">512×512 (快速)</option>
                            <option value="768x768">768×768 (中等)</option>
                            <option value="1024x1024" selected>1024×1024 (标准)</option>
                            <option value="1152x864">1152×864 (横向)</option>
                            <option value="864x1152">864×1152 (纵向)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="guidance_scale">引导强度</label>
                        <input type="number" id="guidance_scale" name="guidance_scale" 
                               value="7.5" min="1" max="20" step="0.5">
                        <div class="help-text">1-20，越高越符合提示词</div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="seed">随机种子 (可选)</label>
                        <input type="number" id="seed" name="seed" placeholder="留空为随机">
                        <div class="help-text">固定数字可重现相同结果</div>
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="watermark" name="watermark">
                            <label for="watermark">添加水印</label>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    🚀 生成图像
                </button>
            </form>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在生成图像，请稍候...</p>
            </div>
            
            <div class="examples">
                <h3>💡 提示词示例</h3>
                <div class="example-item" onclick="setPrompt('一只可爱的橘猫坐在阳光下，水彩画风格')">
                    🐱 一只可爱的橘猫坐在阳光下，水彩画风格
                </div>
                <div class="example-item" onclick="setPrompt('未来城市夜景，霓虹灯闪烁，赛博朋克风格')">
                    🌃 未来城市夜景，霓虹灯闪烁，赛博朋克风格
                </div>
                <div class="example-item" onclick="setPrompt('山水画风格的自然风景，水墨画质感，宁静氛围')">
                    🏞️ 山水画风格的自然风景，水墨画质感，宁静氛围
                </div>
                <div class="example-item" onclick="setPrompt('抽象艺术，色彩丰富，几何图形，现代风格')">
                    🎨 抽象艺术，色彩丰富，几何图形，现代风格
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function setPrompt(text) {
            document.getElementById('prompt').value = text;
        }
        
        document.getElementById('generateForm').addEventListener('submit', function() {
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('submitBtn').textContent = '生成中...';
            document.getElementById('loading').classList.add('show');
        });
    </script>
</body>
</html>
