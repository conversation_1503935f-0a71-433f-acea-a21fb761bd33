<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成结果 - 豆包图像生成</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .result-success {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .result-error {
            background: #fee;
            border: 2px solid #fcc;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .result-error h2 {
            color: #c33;
            margin-bottom: 10px;
        }
        
        .result-error p {
            color: #666;
        }
        
        .image-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .generated-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .image-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .info-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .prompt-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .prompt-display h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .prompt-text {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            font-style: italic;
            color: #555;
        }
        
        .back-link {
            text-align: center;
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .image-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 生成结果</h1>
        </div>
        
        <div class="content">
            {% if result.success %}
                <div class="result-success">
                    <h2 style="color: #28a745; margin-bottom: 20px;">✅ 图像生成成功！</h2>
                    
                    <div class="image-container">
                        <img src="{{ result.image_url }}" alt="生成的图像" class="generated-image" id="generatedImage">
                        
                        <div class="image-actions">
                            <a href="{{ result.image_url }}" target="_blank" class="btn btn-primary">
                                🔍 查看原图
                            </a>
                            <button onclick="downloadImage()" class="btn btn-success">
                                💾 下载图像
                            </button>
                            <button onclick="copyUrl()" class="btn btn-secondary">
                                📋 复制链接
                            </button>
                        </div>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-card">
                            <h3>📊 生成信息</h3>
                            <div class="info-item">
                                <span class="info-label">生成时间:</span>
                                <span class="info-value">{{ "%.2f"|format(result.generation_time) }} 秒</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">图像尺寸:</span>
                                <span class="info-value">{{ result.params.size }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">水印:</span>
                                <span class="info-value">{{ "是" if result.params.watermark else "否" }}</span>
                            </div>
                        </div>
                        
                        <div class="info-card">
                            <h3>⚙️ 参数设置</h3>
                            {% if result.params.guidance_scale %}
                            <div class="info-item">
                                <span class="info-label">引导强度:</span>
                                <span class="info-value">{{ result.params.guidance_scale }}</span>
                            </div>
                            {% endif %}
                            {% if result.params.seed %}
                            <div class="info-item">
                                <span class="info-label">随机种子:</span>
                                <span class="info-value">{{ result.params.seed }}</span>
                            </div>
                            {% endif %}
                            <div class="info-item">
                                <span class="info-label">响应格式:</span>
                                <span class="info-value">{{ result.params.response_format }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="result-error">
                    <h2>❌ 生成失败</h2>
                    <p><strong>错误信息:</strong> {{ result.error }}</p>
                    {% if result.generation_time %}
                    <p><strong>耗时:</strong> {{ "%.2f"|format(result.generation_time) }} 秒</p>
                    {% endif %}
                </div>
            {% endif %}
            
            <div class="prompt-display">
                <h3>📝 使用的提示词</h3>
                <div class="prompt-text">{{ prompt }}</div>
            </div>
            
            <div class="back-link">
                <a href="/" class="btn btn-primary">🔄 重新生成</a>
            </div>
        </div>
    </div>
    
    <script>
        function downloadImage() {
            const imageUrl = "{{ result.image_url }}";
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = 'doubao-generated-image.jpg';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function copyUrl() {
            const imageUrl = "{{ result.image_url }}";
            navigator.clipboard.writeText(imageUrl).then(function() {
                alert('图像链接已复制到剪贴板！');
            }, function(err) {
                console.error('复制失败: ', err);
                alert('复制失败，请手动复制链接');
            });
        }
    </script>
</body>
</html>
