import os
import time
from typing import Optional
from fastapi import FastAPI, Form, Request, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from volcenginesdkarkruntime import Ark

# 配置
DEFAULT_PARAMS = {
    "size": "1024x1024",
    "response_format": "url",
    "watermark": False,
}

MODEL = "doubao-seedream-3-0-t2i-250415"
BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"

# FastAPI应用
app = FastAPI(title="豆包图像生成", description="基于豆包AI的图像生成服务")

# 模板配置
templates = Jinja2Templates(directory="templates")

# 数据模型
class ImageRequest(BaseModel):
    prompt: str
    size: Optional[str] = "1024x1024"
    guidance_scale: Optional[float] = 7.5
    seed: Optional[int] = None
    watermark: Optional[bool] = False

class ImageResponse(BaseModel):
    success: bool
    image_url: Optional[str] = None
    error: Optional[str] = None
    generation_time: Optional[float] = None
    params: Optional[dict] = None

# 图像生成函数
def generate_image_api(prompt: str, **params) -> ImageResponse:
    """API版本的图像生成函数"""
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        return ImageResponse(
            success=False,
            error="未找到 ARK_API_KEY 环境变量"
        )
    
    client = Ark(base_url=BASE_URL, api_key=api_key)
    
    # 合并参数
    final_params = DEFAULT_PARAMS.copy()
    final_params.update(params)
    final_params = {k: v for k, v in final_params.items() if v is not None}
    
    start_time = time.time()
    
    try:
        response = client.images.generate(
            model=MODEL,
            prompt=prompt,
            **final_params
        )
        
        generation_time = time.time() - start_time
        
        return ImageResponse(
            success=True,
            image_url=response.data[0].url,
            generation_time=generation_time,
            params=final_params
        )
        
    except Exception as e:
        generation_time = time.time() - start_time
        return ImageResponse(
            success=False,
            error=str(e),
            generation_time=generation_time
        )

# 路由
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/generate", response_model=ImageResponse)
async def generate_image_endpoint(request: ImageRequest):
    """API接口：生成图像"""
    return generate_image_api(
        prompt=request.prompt,
        size=request.size,
        guidance_scale=request.guidance_scale,
        seed=request.seed,
        watermark=request.watermark
    )

@app.post("/generate-form")
async def generate_image_form(
    request: Request,
    prompt: str = Form(...),
    size: str = Form("1024x1024"),
    guidance_scale: float = Form(7.5),
    seed: Optional[int] = Form(None),
    watermark: bool = Form(False)
):
    """表单提交：生成图像"""
    result = generate_image_api(
        prompt=prompt,
        size=size,
        guidance_scale=guidance_scale,
        seed=seed,
        watermark=watermark
    )
    
    return templates.TemplateResponse("result.html", {
        "request": request,
        "result": result,
        "prompt": prompt
    })

@app.get("/api/health")
async def health_check():
    """健康检查"""
    api_key = os.environ.get("ARK_API_KEY")
    return {
        "status": "ok",
        "api_key_configured": bool(api_key),
        "model": MODEL
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
