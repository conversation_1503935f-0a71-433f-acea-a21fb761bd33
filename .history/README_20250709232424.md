# 豆包图像生成项目

使用豆包AI生成图像的Python项目，基于uv包管理器。

## 快速开始

### 1. 安装依赖
```bash
uv sync
```

### 2. 配置API密钥
在`.env`文件中设置：
```
ARK_API_KEY=your_api_key_here
```

### 3. 运行程序

**命令行版本:**
```bash
uv run python main.py
```

**Web服务版本:**
```bash
# 启动Web服务
uv run python start_server.py

# 访问Web界面
http://localhost:8000
```

## 功能特性

### 🖥️ 命令行版本
- 简单直接的图像生成
- 支持参数自定义
- 适合脚本集成

### 🌐 Web服务版本
- 美观的Web界面
- 实时参数调整
- 图像预览和下载
- RESTful API接口

## 参数配置

### 可用参数

| 参数 | 说明 | 可选值 |
|------|------|--------|
| `prompt` | 提示词 | 任意文本描述 |
| `size` | 图像尺寸 | `512x512`, `768x768`, `1024x1024`, `1152x864`, `864x1152` |
| `guidance_scale` | 引导强度 | 1.0-20.0 (推荐7.5) |
| `seed` | 随机种子 | 数字(固定) 或 None(随机) |
| `watermark` | 水印 | true/false |

### 配置方法

**命令行版本:** 编辑 `config.py` 或修改 `main.py` 中的参数
**Web版本:** 通过Web界面直接调整参数

## API使用

### Web界面
访问 `http://localhost:8000` 使用图形界面

### REST API
```bash
# 生成图像
curl -X POST "http://localhost:8000/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "一只可爱的猫咪",
    "size": "1024x1024",
    "guidance_scale": 7.5,
    "watermark": false
  }'

# 健康检查
curl http://localhost:8000/api/health
```

### API文档
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 常用命令

```bash
# 启动Web服务
uv run python start_server.py

# 测试API
uv run python test_api.py

# 命令行生成
uv run python main.py

# 查看示例
uv run python examples.py
```

## 故障排除

- **API密钥错误**: 检查`.env`文件中的`ARK_API_KEY`
- **网络问题**: 确保可以访问火山引擎API
- **依赖问题**: 运行`uv sync --reinstall`重新安装

## 项目特点

- ✅ 使用uv进行现代化依赖管理
- ✅ 支持丰富的图像生成参数配置
- ✅ 简洁的代码结构，易于理解和修改
- ✅ 完整的错误处理和用户提示
