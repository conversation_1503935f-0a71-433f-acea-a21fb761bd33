#!/usr/bin/env python3
"""
豆包图像生成API测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_generate_api():
    """测试图像生成API接口"""
    print("\n🎨 测试图像生成API...")
    
    payload = {
        "prompt": "一只可爱的橘猫",
        "size": "512x512",
        "guidance_scale": 7.5,
        "watermark": False
    }
    
    try:
        print(f"📝 请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/generate",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        request_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 生成成功!")
            print(f"⏱️  请求耗时: {request_time:.2f}秒")
            print(f"🖼️  图像URL: {data.get('image_url', 'N/A')}")
            print(f"📊 生成时间: {data.get('generation_time', 'N/A')}秒")
            return True
        else:
            print(f"❌ 生成失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_different_params():
    """测试不同参数组合"""
    print("\n🧪 测试不同参数组合...")
    
    test_cases = [
        {
            "name": "小尺寸快速生成",
            "params": {
                "prompt": "简单的几何图形",
                "size": "512x512",
                "guidance_scale": 5.0
            }
        },
        {
            "name": "固定种子测试",
            "params": {
                "prompt": "抽象艺术",
                "size": "768x768",
                "seed": 42,
                "guidance_scale": 7.5
            }
        },
        {
            "name": "高引导强度",
            "params": {
                "prompt": "详细的风景画",
                "size": "1024x1024",
                "guidance_scale": 12.0,
                "watermark": True
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/generate",
                json=test_case['params'],
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 成功 - 耗时: {data.get('generation_time', 'N/A')}秒")
                else:
                    print(f"❌ 失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 避免请求过于频繁
        time.sleep(1)

def main():
    print("🧪 豆包图像生成API测试")
    print("=" * 50)
    
    # 测试健康检查
    if not test_health():
        print("❌ 服务未启动或配置有误")
        print("💡 请先运行: uv run python start_server.py")
        return
    
    # 测试基础API
    if not test_generate_api():
        print("❌ 基础API测试失败")
        return
    
    # 测试不同参数
    test_different_params()
    
    print("\n" + "=" * 50)
    print("🎉 API测试完成!")
    print("💡 您可以访问 http://localhost:8000 使用Web界面")
    print("📚 API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
