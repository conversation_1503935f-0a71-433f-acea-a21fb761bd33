# 豆包图像生成 - Web服务指南

## 🌐 Web服务概述

基于FastAPI构建的现代化Web服务，提供美观的用户界面和完整的RESTful API。

## 🚀 快速启动

### 1. 启动服务
```bash
# 方法1: 使用启动脚本（推荐）
uv run python start_server.py

# 方法2: 直接启动
uv run python app.py

# 方法3: 使用uvicorn
uv run uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 2. 访问服务
- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/health

## 🎨 Web界面功能

### 主要特性
- ✅ 响应式设计，支持移动端
- ✅ 实时参数调整
- ✅ 提示词示例库
- ✅ 图像预览和下载
- ✅ 生成历史记录
- ✅ 错误处理和用户提示

### 界面组件
1. **提示词输入区**: 支持多行文本，提供示例
2. **参数配置区**: 图像尺寸、引导强度、种子等
3. **生成按钮**: 一键生成，显示进度
4. **结果展示区**: 图像预览、下载、分享

### 使用流程
1. 输入提示词描述
2. 调整生成参数
3. 点击生成按钮
4. 查看结果并下载

## 📡 API接口

### 核心接口

#### 1. 生成图像
```http
POST /generate
Content-Type: application/json

{
  "prompt": "一只可爱的猫咪",
  "size": "1024x1024",
  "guidance_scale": 7.5,
  "seed": 42,
  "watermark": false
}
```

**响应示例:**
```json
{
  "success": true,
  "image_url": "https://...",
  "generation_time": 2.5,
  "params": {
    "size": "1024x1024",
    "watermark": false
  }
}
```

#### 2. 表单提交
```http
POST /generate-form
Content-Type: application/x-www-form-urlencoded

prompt=一只猫&size=1024x1024&guidance_scale=7.5
```

#### 3. 健康检查
```http
GET /api/health
```

**响应:**
```json
{
  "status": "ok",
  "api_key_configured": true,
  "model": "doubao-seedream-3-0-t2i-250415"
}
```

### 参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `prompt` | string | ✅ | - | 图像描述文本 |
| `size` | string | ❌ | `1024x1024` | 图像尺寸 |
| `guidance_scale` | float | ❌ | `7.5` | 引导强度 |
| `seed` | integer | ❌ | `null` | 随机种子 |
| `watermark` | boolean | ❌ | `false` | 是否添加水印 |

## 🔧 开发和测试

### 测试工具
```bash
# API功能测试
uv run python test_api.py

# 命令行版本
uv run python main.py

# 示例代码
uv run python examples.py
```

### 开发模式
```bash
# 启动开发服务器（自动重载）
uv run uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 日志和监控
- 服务启动日志
- 请求响应时间
- 错误信息记录
- API调用统计

## 🎯 使用场景

### 1. 个人创作
- 艺术创作灵感
- 社交媒体内容
- 个人项目配图

### 2. 开发集成
- 应用内图像生成
- 批量内容创建
- 自动化工作流

### 3. 企业应用
- 营销素材制作
- 产品原型设计
- 内容管理系统

## 🔒 安全和配置

### 环境变量
```bash
# 必需配置
export ARK_API_KEY="your_api_key_here"

# 可选配置
export HOST="0.0.0.0"
export PORT="8000"
export LOG_LEVEL="info"
```

### 安全建议
- 不要在客户端暴露API密钥
- 使用HTTPS部署生产环境
- 实施请求频率限制
- 添加用户认证机制

## 📊 性能优化

### 响应时间
- 平均生成时间: 2-5秒
- API响应时间: <100ms
- 页面加载时间: <2秒

### 优化建议
- 使用CDN加速静态资源
- 实施缓存策略
- 异步处理长时间任务
- 负载均衡多实例部署

## 🚀 部署指南

### 本地部署
```bash
# 克隆项目
git clone <repository>
cd doubao-image

# 安装依赖
uv sync

# 配置环境
export ARK_API_KEY="your_key"

# 启动服务
uv run python start_server.py
```

### Docker部署
```dockerfile
FROM python:3.12-slim
WORKDIR /app
COPY . .
RUN pip install uv && uv sync
EXPOSE 8000
CMD ["uv", "run", "python", "start_server.py"]
```

### 云服务部署
- 支持Heroku、Railway、Vercel等平台
- 配置环境变量
- 设置启动命令

## 📚 扩展功能

### 计划中的功能
- [ ] 用户认证系统
- [ ] 生成历史管理
- [ ] 批量生成功能
- [ ] 图像编辑工具
- [ ] 社区分享功能

### 自定义开发
- 修改UI样式和布局
- 添加新的参数选项
- 集成其他AI模型
- 扩展API功能

---

**🎉 恭喜！您的豆包图像生成Web服务已经完全配置完成！**

现在您可以通过美观的Web界面或强大的API来生成高质量的AI图像。
