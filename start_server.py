#!/usr/bin/env python3
"""
豆包图像生成服务启动脚本
"""

import os
import sys
import uvicorn
from app import app

def check_environment():
    """检查环境配置"""
    api_key = os.environ.get("ARK_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到 ARK_API_KEY 环境变量")
        print("💡 请设置API密钥: export ARK_API_KEY='your_api_key'")
        return False
    
    print(f"✅ API密钥已配置: {api_key[:8]}...{api_key[-4:]}")
    return True

def main():
    print("🚀 豆包图像生成服务")
    print("=" * 40)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    print("🌐 启动Web服务...")
    print("📱 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔧 健康检查: http://localhost:8000/api/health")
    print("=" * 40)
    print("按 Ctrl+C 停止服务")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True,  # 开发模式，代码变更自动重载
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")

if __name__ == "__main__":
    main()
